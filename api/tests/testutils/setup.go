package testutils

import (
	"kids-platform/pkg/config"
	"kids-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// InitTestEnvironment 初始化测试环境
// 统一处理logger初始化、Gin模式设置等
func InitTestEnvironment() {
	// 初始化logger（避免重复初始化）
	if !logger.IsInitialized() {
		logger.Init(config.LogConfig{
			Level:  "debug",
			Format: "text",
			Output: "stdout",
		})
	}

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)
}

// CreateTestDB 创建测试数据库
func CreateTestDB() (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	return db, nil
}

// GetTestConfig 获取测试配置
func GetTestConfig() *config.Config {
	return &config.Config{
		JWT: config.JWTConfig{
			Secret:      "test_secret_key_for_testing",
			ExpireHours: 24,
		},
		Wechat: config.WechatConfig{
			Miniprogram: config.MiniprogramConfig{
				AppID:          "test_app_id",
				AppSecret:      "test_app_secret",
				MockEnabled:    true,
				MockOpenID:     "test_mock_openid",
				MockSessionKey: "test_mock_session_key",
				MockUnionID:    "test_mock_unionid",
				AuthURL:        "https://api.weixin.qq.com/sns/jscode2session",
				Timeout:        5000,
			},
		},
		Security: config.SecurityConfig{
			ProductionMode: false,
			DebugMode:      true,
		},
	}
}

// GetIntegrationTestConfig 获取集成测试配置
func GetIntegrationTestConfig() *config.Config {
	return &config.Config{
		JWT: config.JWTConfig{
			Secret:      "integration_test_secret_key",
			ExpireHours: 24,
		},
		Wechat: config.WechatConfig{
			Miniprogram: config.MiniprogramConfig{
				AppID:          "integration_test_app_id",
				AppSecret:      "integration_test_app_secret",
				MockEnabled:    true,
				MockOpenID:     "integration_test_openid",
				MockSessionKey: "integration_test_session_key",
				MockUnionID:    "integration_test_unionid",
				AuthURL:        "https://api.weixin.qq.com/sns/jscode2session",
				Timeout:        5000,
			},
		},
		Security: config.SecurityConfig{
			ProductionMode: false,
			DebugMode:      true,
		},
	}
}

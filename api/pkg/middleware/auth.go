package middleware

import (
	"fmt"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/jwt"
	"kids-platform/pkg/response"
	"strings"

	"github.com/gin-gonic/gin"
)

// JWTAuth JWT认证中间件
func JWTAuth(jwtManager *jwt.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.Get<PERSON>eader("Authorization")
		if token == "" {
			response.ErrorWithCode(c, errcode.ErrUnauthorized.WithDetails("缺少认证token"))
			c.Abort()
			return
		}

		// 移除Bearer前缀
		token = strings.TrimPrefix(token, "Bearer ")
		if token == "" {
			response.ErrorWithCode(c, errcode.ErrUnauthorized.WithDetails("无效的token格式"))
			c.Abort()
			return
		}

		// 验证JWT token
		claims, err := jwtManager.ValidateToken(token)
		if err != nil {
			response.ErrorWithCode(c, errcode.ErrInvalidToken.WithDetails("token验证失败", err.Error()))
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_type", claims.UserType)
		c.Set("claims", claims)
		fmt.Println(claims.UserID)
		c.Next()
	}
}

// AdminAuth 管理员认证中间件
func AdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户类型
		userType, exists := c.Get("user_type")
		if !exists {
			response.ErrorWithCode(c, errcode.ErrForbidden.WithDetails("用户信息不存在"))
			c.Abort()
			return
		}

		// 检查是否为管理员（用户类型3为管理员）
		if userType.(uint8) != 3 {
			response.ErrorWithCode(c, errcode.ErrForbidden.WithDetails("需要管理员权限"))
			c.Abort()
			return
		}

		c.Next()
	}
}

// OptionalAuth 可选认证中间件（不强制要求认证）
func OptionalAuth(jwtManager *jwt.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" {
			// 没有token，继续执行但不设置用户信息
			c.Next()
			return
		}

		// 移除Bearer前缀
		token = strings.TrimPrefix(token, "Bearer ")
		if token == "" {
			c.Next()
			return
		}

		// 尝试验证JWT token
		claims, err := jwtManager.ValidateToken(token)
		if err != nil {
			// token无效，继续执行但不设置用户信息
			c.Next()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_type", claims.UserType)
		c.Set("claims", claims)

		c.Next()
	}
}

// GetCurrentUserID 从上下文中获取当前用户ID
func GetCurrentUserID(c *gin.Context) (uint64, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}
	return userID.(uint64), true
}

// GetCurrentUser 从上下文中获取当前用户信息
func GetCurrentUser(c *gin.Context) (*jwt.Claims, bool) {
	claims, exists := c.Get("claims")
	if !exists {
		return nil, false
	}
	return claims.(*jwt.Claims), true
}

// RequireAuth 检查是否已认证的辅助函数
func RequireAuth(c *gin.Context) bool {
	_, exists := c.Get("user_id")
	return exists
}

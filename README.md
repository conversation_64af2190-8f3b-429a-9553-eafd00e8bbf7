# 🚀 跳跳星球

> 基于AI协作优化的跳跳星球，包含微信小程序前端和Go后端API服务

## 📋 项目概述

这是一个专为儿童跳绳学习设计的全栈平台，采用现代化的架构设计和标准化的开发流程。平台包含微信小程序前端和Go后端服务，支持API/Admin双服务分离，内置代码生成器，为儿童跳绳学习提供完整的解决方案。

### 🎯 核心特性

- **📱 微信小程序**：原生小程序体验，支持打卡、学习、社交
- **�️ 清晰架构**：前后端分离，分层设计，职责明确
- **� 服务分离**：API/Admin双入口，独立部署和扩展
- **🌐 API统一管理**：采用方案B架构，高内聚低耦合
- **⚡ 代码生成**：自动生成标准CRUD模块，提高开发效率
- **📋 规范统一**：内置MVS规则，确保代码质量
- **� 开箱即用**：完整的配置和工具链，快速启动项目

## 🏗️ 技术架构

### 系统组成
- **微信小程序前端**：原生小程序，提供用户交互界面
- **API服务**：面向用户端，提供业务API接口
- **Admin服务**：面向管理端，提供管理功能接口
- **共享层**：Service/Repository/Model层共享复用

### 前端技术栈
- **框架**：微信小程序原生开发
- **状态管理**：自研状态管理器
- **API管理**：统一API模块（方案B架构）
- **UI组件**：自定义组件库
- **工具函数**：完整的utils工具集

### 后端技术栈
- **Web框架**：Gin
- **ORM**：GORM
- **数据库**：MySQL/PostgreSQL（可配置）
- **缓存**：Redis（可选）
- **配置**：Viper
- **日志**：Logrus
- **测试**：Testify

## 📁 项目结构

```
.
├── README.md                    # 项目说明
├── docs/                        # 📚 项目文档
│   ├── README.md               # 文档导航
│   ├── ARCHITECTURE.md         # 项目架构
│   ├── PROJECT_DIRECTORY_STRUCTURE.md  # 目录结构规范
│   ├── standards/              # 📋 项目标准
│   │   ├── collaboration/     # 协作规范
│   │   ├── development/       # 开发标准
│   │   └── templates/         # 模板库
│   ├── knowledge/              # 🧠 核心知识库
│   │   ├── mvs-core-rules.md  # MVS开发规则
│   │   ├── ai-collaboration-guide.md  # AI协作指南
│   │   ├── decisions/         # 架构决策记录
│   │   ├── patterns/          # 设计模式
│   │   └── templates/         # 文档模板
│   └── reports/                # 📊 项目报告
│       ├── OPTIMIZATION_COMPLETION_REPORT.md
│       └── PKG_BUG_FIXES_REPORT.md
├── miniprogram/                 # 📱 微信小程序前端
│   ├── apis/                   # 🌐 API统一管理
│   │   ├── auth.js            # 认证相关API
│   │   ├── user.js            # 用户管理API
│   │   ├── children.js        # 孩子管理API
│   │   ├── checkin.js         # 打卡系统API
│   │   ├── content.js         # 内容管理API
│   │   └── index.js           # 统一导出
│   ├── pages/                  # 页面文件
│   │   ├── home/              # 首页
│   │   ├── login/             # 登录页
│   │   ├── profile/           # 个人中心
│   │   └── growth/            # 成长记录
│   ├── components/             # 自定义组件
│   ├── utils/                  # 工具函数
│   │   ├── request.js         # HTTP请求封装
│   │   ├── constants.js       # 常量定义
│   │   ├── state-manager.js   # 状态管理
│   │   └── index.js           # 统一导出
│   ├── docs/                   # 前端文档
│   │   └── api-management-guide.md  # API管理指南
│   ├── app.js                  # 小程序入口
│   ├── app.json               # 小程序配置
│   └── project.config.json    # 项目配置
├── api/                         # 🔧 后端API框架
│   ├── cmd/                    # 应用入口
│   │   ├── api-server/        # API服务入口
│   │   └── admin-server/      # 管理后台入口
│   ├── internal/              # 内部代码
│   │   ├── handlers/          # HTTP处理器
│   │   ├── services/          # 业务逻辑层
│   │   ├── repositories/      # 数据访问层
│   │   └── models/           # 数据模型
│   ├── pkg/                   # 可复用包
│   ├── configs/              # 配置文件
│   ├── database/            # 数据库相关
│   └── scripts/             # 脚本工具
└── scripts/                     # 🛠️ 构建和部署脚本
```

## 🚀 快速开始

### 开发环境要求
- **前端**：微信开发者工具
- **后端**：Go 1.23.10+
- **数据库**：MySQL 8.0+ / PostgreSQL 13+
- **缓存**：Redis 6.0+ (可选)
- **日志**：MongoDB 4.4+ (可选)

### 启动步骤

#### 1. 后端服务启动
```bash
# 克隆项目
git clone <repository-url>
cd kids-platform

# 初始化后端项目
cd api
go mod tidy
cp configs/config.example.yaml configs/config.yaml

# 启动API服务 (端口8080)
go run cmd/api-server/main.go

# 启动Admin服务 (端口8081)
go run cmd/admin-server/main.go
```

#### 2. 微信小程序启动
```bash
# 进入小程序目录
cd miniprogram

# 使用微信开发者工具打开项目
# 1. 打开微信开发者工具
# 2. 选择"导入项目"
# 3. 选择miniprogram目录
# 4. 配置AppID（测试号即可）
# 5. 点击"确定"开始开发
```

#### 3. 开发工具
```bash
# 使用代码生成器创建新模块
./scripts/generate.sh user

# API文档生成
cd api && swag init
```

## 🌐 API统一管理

### 架构设计

采用**方案B（端点内部定义）**架构，实现高内聚、低耦合的模块化设计：

```javascript
// 每个API模块内部定义自己的端点
// apis/auth.js
const ENDPOINTS = {
  WECHAT_LOGIN: `${API.BASE_URL}/auth/wechat/login`,
  REFRESH_TOKEN: `${API.BASE_URL}/auth/refresh`
};

const authAPI = {
  async wechatLogin(params) {
    return http.post(ENDPOINTS.WECHAT_LOGIN, params);
  }
};
```

### 使用方式

```javascript
// 导入API模块
const { authAPI, userAPI, api } = require('../../utils/index.js');

// 方式1: 直接使用具体API（推荐）
const loginResult = await authAPI.wechatLogin({ code: 'wx_code' });

// 方式2: 使用api对象
const userInfo = await api.user.getCurrentUser();
```

### 架构优势

- ✅ **高内聚**：端点定义与使用在同一文件
- ✅ **低耦合**：修改某个模块不影响其他模块
- ✅ **易维护**：开发时只需关注当前模块
- ✅ **便测试**：每个模块可独立测试

详细文档：[API管理指南](miniprogram/docs/api-management-guide.md)

## 🤖 AI协作指南

### AI助手启动检查清单
- [ ] 阅读 [`docs/knowledge/README.md`](docs/knowledge/README.md) 了解项目核心资产
- [ ] 阅读 [`docs/knowledge/mvs-core-rules.md`](docs/knowledge/mvs-core-rules.md) 理解开发标准
- [ ] 阅读 [`docs/knowledge/ai-collaboration-guide.md`](docs/knowledge/ai-collaboration-guide.md) 了解协作方式
- [ ] 查阅 [`docs/knowledge/decisions/`](docs/knowledge/decisions/) 了解重要决策

### 开发规范
- **严格遵守**：MVS核心规则（6条）
- **主动应用**：场景化协作模式
- **及时记录**：重要决策通过ADR流程
- **使用模板**：提高文档和代码质量

## �️ 代码生成器

### 支持的模块类型
- **CRUD模块**：自动生成完整的增删改查功能
- **认证模块**：用户认证和权限管理
- **文件模块**：文件上传和管理
- **通知模块**：消息推送和通知

### 生成器命令
```bash
# 生成基础CRUD模块
./scripts/generate.sh user

# 生成带关联的模块
./scripts/generate.sh order --relations=user,product

# 生成API和Admin双端点
./scripts/generate.sh product --endpoints=api,admin
```

## 📊 框架特性

### 已实现功能
- [x] 基础项目结构和配置
- [x] API/Admin双服务架构
- [x] 统一的错误处理和响应格式
- [x] 数据库连接和迁移支持
- [x] 日志和配置管理
- [x] MVS规则集成

### 开发计划
1. **代码生成器**（1周）
2. **认证和权限模块**（1周）
3. **文件上传模块**（3天）
4. **监控和部署工具**（1周）

## 📚 相关文档

### 核心文档
- [项目文档导航](docs/README.md)
- [知识库](docs/knowledge/README.md)
- [MVS核心规则](docs/knowledge/mvs-core-rules.md)
- [AI协作指南](docs/knowledge/ai-collaboration-guide.md)

### 开发文档
- [项目开发流程](docs/knowledge/decisions/adr-002-project-development-workflow.md)
- [需求确认模板](docs/knowledge/templates/requirements-template.md)
- [提交前检查清单](docs/knowledge/templates/checklists/pre-commit-checklist.md)

## 🎯 开始开发

准备好开始开发了吗？

1. **AI助手**：按照启动检查清单快速恢复上下文
2. **开发者**：阅读相关文档了解框架标准和流程
3. **团队**：严格遵守MVS规则确保代码质量
4. **生成器**：使用代码生成器快速创建标准模块

---

**让我们一起构建高质量的Go API框架！** 🎯

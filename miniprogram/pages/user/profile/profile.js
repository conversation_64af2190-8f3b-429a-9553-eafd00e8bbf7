const { loginGuard, userActions } = require("../../../utils/index.js");
const {
  childrenActions,
  dataActions,
} = require("../../../utils/state-actions.js");

// 我的页面 - 个人管理中心
const app = getApp();

// 工具函数：检测是否为emoji头像
function isEmojiAvatar(avatar) {
  if (!avatar) return false;
  // 检测是否为emoji字符（简单检测：长度较短且不包含http/https）
  return (
    avatar.length <= 4 && !avatar.startsWith("http") && !avatar.startsWith("/")
  );
}

Page({
  ...loginGuard,
  data: {
    userInfo: {},
    currentChild: {},
    stats: {
      activeCamps: 0,
      totalBadges: 0,
      continuousDays: 0,
      ranking: "--",
    },
  },

  async onLoad() {
    console.log("我的页面加载");

    // 调试：检查当前登录状态
    const currentLoginStatus = userActions.isLoggedIn();
    const currentUserInfo = userActions.getUserInfo();
    console.log("🔍 当前登录状态:", currentLoginStatus);
    console.log("🔍 当前用户信息:", currentUserInfo);
    console.log(
      "🔍 是否有昵称:",
      !!(currentUserInfo && currentUserInfo.nickname)
    );

    // 检查登录状态，未登录会自动处理
    const isLoggedIn = await this.ensureLogin({
      required: true, // 是否必须登录
      needUserInfo: true, // 是否需要用户信息
      silent: false, // 是否静默处理
    });

    console.log("🔍 ensureLogin结果:", isLoggedIn);

    if (!isLoggedIn) {
      console.log("🔍 未通过登录检查，应该跳转到登录页面");
      return; // 已跳转到登录页面
    }

    console.log("🔍 通过登录检查，初始化页面");
    this.initPage();
  },

  async onShow() {
    console.log("我的页面显示");
    try {
      // 刷新所有用户相关数据
      await dataActions.refreshAllUserData();
      // 重新加载页面数据
      this.refreshData();
    } catch (error) {
      console.error("刷新数据失败:", error);
      // 即使刷新失败，也要加载本地缓存的数据
      this.refreshData();
    }
  },

  // 初始化页面
  initPage() {
    this.loadUserInfo();
    this.loadCurrentChild();
    this.loadStats();
  },

  // 刷新数据
  refreshData() {
    this.loadUserInfo();
    this.loadCurrentChild();
    this.loadStats();
  },

  // 加载用户信息
  loadUserInfo() {
    // 从状态管理器获取用户信息
    const userInfo = userActions.getUserInfo();
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
      });
    }
  },

  // 加载当前选中的孩子
  loadCurrentChild() {
    // 从状态管理器获取当前孩子
    const currentChild = childrenActions.getCurrentChild();
    if (currentChild) {
      // 模拟加载孩子的统计数据
      const childWithStats = {
        ...currentChild,
        totalCheckins: 15,
        totalPoints: 320,
        isEmojiAvatar: isEmojiAvatar(currentChild.avatar), // 标记是否为emoji头像
      };

      this.setData({
        currentChild: childWithStats,
      });
    } else {
      this.setData({
        currentChild: {},
      });
    }
  },

  // 加载统计数据
  loadStats() {
    if (!this.data.currentChild.id) {
      this.setData({
        stats: {
          activeCamps: 0,
          totalBadges: 0,
          continuousDays: 0,
          ranking: "--",
        },
      });
      return;
    }

    // 模拟统计数据，实际应该从API获取
    const stats = {
      activeCamps: 2,
      totalBadges: 5,
      continuousDays: 8,
      ranking: 15,
    };

    this.setData({
      stats: stats,
    });
  },

  // 编辑用户信息
  editUserInfo() {
    wx.showToast({
      title: "功能开发中",
      icon: "none",
    });
  },

  // 跳转到孩子管理
  goToChildManage() {
    wx.navigateTo({
      url: "/pages/child-manage/child-manage",
    });
  },

  // 跳转到添加孩子
  goToAddChild() {
    wx.navigateTo({
      url: "/pages/child-create/child-create",
    });
  },

  // 跳转到数据详情
  goToDataDetail() {
    if (!this.data.currentChild.id) {
      wx.showToast({
        title: "请先添加孩子信息",
        icon: "none",
      });
      return;
    }

    wx.navigateTo({
      url: "/pages/data-detail/data-detail",
    });
  },

  // 跳转到排行榜
  goToLeaderboard() {
    wx.navigateTo({
      url: "/pages/leaderboard/leaderboard",
    });
  },

  // 跳转到我的勋章
  goToBadges() {
    wx.navigateTo({
      url: "/pages/badges/badges",
    });
  },

  // 跳转到邀请好友
  goToInvite() {
    wx.navigateTo({
      url: "/pages/invite/invite",
    });
  },

  // 跳转到分享卡片
  goToShareCard() {
    wx.navigateTo({
      url: "/pages/share-card/share-card",
    });
  },

  // 跳转到设置
  goToSettings() {
    wx.navigateTo({
      url: "/pages/settings/settings",
    });
  },

  // 显示关于我们
  showAbout() {
    wx.showModal({
      title: "关于我们",
      content:
        "跳跳星球 v1.0.0\n\n让孩子爱上运动，健康成长！\n\n专为K-6年级儿童设计的跳绳学习平台，通过游戏化的方式培养孩子的运动习惯。",
      showCancel: false,
      confirmText: "我知道了",
    });
  },

  // 显示意见反馈
  showFeedback() {
    wx.showModal({
      title: "意见反馈",
      content:
        "如有任何问题或建议，请联系我们：\n\n微信客服：kefu001\n邮箱：<EMAIL>",
      showCancel: false,
      confirmText: "我知道了",
    });
  },

  // 显示帮助中心
  showHelp() {
    wx.showModal({
      title: "帮助中心",
      content:
        "常见问题：\n\n1. 如何添加孩子信息？\n2. 如何参与训练营？\n3. 如何进行打卡？\n4. 如何获得勋章？\n\n更多帮助请联系客服。",
      showCancel: false,
      confirmText: "我知道了",
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: "确认退出",
      content: "确定要退出登录吗？",
      success: (res) => {
        if (res.confirm) {
          app.logout();
        }
      },
    });
  },

  // 分享
  onShareAppMessage() {
    const child = this.data.currentChild;
    if (child.name) {
      return {
        title: `${child.name}在跳跳星球已经打卡${child.totalCheckins}次了！`,
        path: "/pages/home/<USER>",
        image_url: "/images/share-profile.png",
      };
    }

    return {
      title: "跳跳星球 - 让孩子爱上运动",
      path: "/pages/home/<USER>",
      image_url: "/images/share-profile.png",
    };
  },

  onShareTimeline() {
    return {
      title: "跳跳星球 - 让孩子爱上运动",
      image_url: "/images/share-profile.png",
    };
  },
});

/* 我的页面样式 - WeUI风格 */

.page {
  min-height: 100vh;
  background-color: var(--weui-BG-1);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, var(--weui-BRAND) 0%, #4A90E2 100%);
  padding: 60rpx 30rpx 40rpx;
  margin-bottom: 20rpx;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.default-user-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: white;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 10rpx;
}

.user-id {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 孩子头像 */
.child-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  overflow: hidden;
  margin-right: 20rpx;
  background-color: var(--weui-BG-1);
}

.child-avatar image {
  width: 100%;
  height: 100%;
}

.default-child-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  background-color: var(--weui-BG-1);
}

/* 孩子信息 */
.child-info {
  display: flex;
  flex-direction: column;
}

.child-name {
  font-size: 34rpx;
  color: var(--weui-FG-0);
  font-weight: 500;
  margin-bottom: 4rpx;
}

.child-age {
  font-size: 28rpx;
  color: var(--weui-FG-1);
}

/* 统计信息 */
.child-stats {
  text-align: right;
}

.stat-text {
  font-size: 28rpx;
  color: var(--weui-FG-1);
}

/* 功能图标 */
.function-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 20rpx;
  background-color: var(--weui-BG-1);
  border-radius: 12rpx;
}

.no-child-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 20rpx;
  background-color: var(--weui-BG-1);
  border-radius: 12rpx;
}

/* WeUI 单元格描述文字 */
.weui-cell__desc {
  font-size: 28rpx;
  color: var(--weui-FG-1);
  line-height: 1.4;
  margin-top: 4rpx;
}

/* 按钮区域 */
.weui-btn-area {
  margin: 60rpx 30rpx 30rpx;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}

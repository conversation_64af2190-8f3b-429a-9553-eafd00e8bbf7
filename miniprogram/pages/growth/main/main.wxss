/* 成长主页重构版样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 数据源信息显示 */
.data-source-info {
  margin: 0 32rpx;
  padding: 20rpx 24rpx;
  background-color: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 12rpx;
  text-align: center;
}

.data-source-text {
  font-size: 24rpx;
  color: #0369a1;
  font-weight: 500;
}

/* 顶部Tab导航 */
.tab-nav {
  background-color: #FFFFFF;
  display: flex;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #E5E5E5;
  position: sticky;
  top: 0;
  z-index: 99;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  position: relative;
}

.tab-item.active {
  color: #FF7A45;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #FF7A45;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.tab-item.active .tab-text {
  color: #FF7A45;
  font-weight: 600;
}

/* Tab内容 */
.tab-content {
  padding: 0 32rpx;
}

/* 通用区块标题 */
.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 32rpx 32rpx 24rpx;
  padding: 0;
  position: relative;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 400;
  color: #333333;
  flex: 1;
}

.camps-count, .medals-count {
  font-size: 26rpx;
  color: #FF7A45;
  font-weight: 500;
  background-color: rgba(255, 122, 69, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.share-all-btn, .share-timeline-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background-color: #FF7A45;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  border-radius: 40rpx;
  border: none;
}

.share-icon {
  font-size: 24rpx;
}

.share-text {
  font-size: 24rpx;
}

/* 训练营列表 */
.camps-section {
  margin-bottom: 48rpx;
}

.camp-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 训练营卡片 */
.camp-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.camp-card:active {
  transform: scale(0.98);
  border-color: #FF7A45;
}

/* 训练营头部 */
.camp-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.camp-info {
  flex: 1;
  cursor: pointer;
}

.camp-info:active {
  opacity: 0.7;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
}

.status-symbol {
  font-size: 24rpx;
  line-height: 1;
  display: block;
  text-align: center;
}

.ranking-link {
  padding: 8rpx 16rpx;
  background-color: #F7F8FA;
  border: 1rpx solid #E5E5E5;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.ranking-link:active {
  background-color: #E8F4FD;
  border-color: #4A90E2;
  transform: scale(0.98);
}

.ranking-text {
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

.ranking-link:active .ranking-text {
  color: #4A90E2;
}

.camp-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.camp-subtitle {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}





/* 进度信息 */
.camp-progress {
  margin-bottom: 20rpx;
}

.progress-data {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.data-item {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

.data-separator {
  font-size: 24rpx;
  color: #CCCCCC;
  font-weight: 400;
}

/* 家庭荣誉契约详情 */
.contract-detail {
  background: linear-gradient(135deg, #F8FAFE, #E8F4FD);
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #E8F4FD;
}

.contract-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.contract-icon {
  font-size: 20rpx;
}

.contract-text {
  font-size: 24rpx;
  color: #4A90E2;
  font-weight: 600;
}

.contract-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.contract-reward,
.contract-witness {
  font-size: 22rpx;
  color: #666666;
  font-weight: 500;
}

/* 操作按钮区 - 打卡与契约并排 */
.action-buttons-row {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.checkin-btn-flex {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 18rpx 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary-soft.checkin-btn-flex {
  background: linear-gradient(135deg, #FF9A6B, #FFB88A);
  color: #FFFFFF;
  box-shadow: 0 2rpx 12rpx rgba(255, 154, 107, 0.2);
}

.btn-success-soft.checkin-btn-flex {
  background: linear-gradient(135deg, #73D13D, #95DE64);
  color: #FFFFFF;
  box-shadow: 0 2rpx 12rpx rgba(115, 209, 61, 0.2);
}

.checkin-btn-flex:active {
  transform: scale(0.98);
}

.checkin-btn-flex .btn-icon {
  font-size: 28rpx;
}

.checkin-btn-flex .btn-text {
  font-size: 28rpx;
  font-weight: 600;
}

.btn-outline-soft.contract-btn-flex {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  padding: 18rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: 1rpx solid #E5E5E5;
  background-color: #F7F8FA;
  color: #666666;
  transition: all 0.3s ease;
}

.btn-outline-soft.contract-btn-flex:active {
  background-color: #E8F4FD;
  border-color: #4A90E2;
  color: #4A90E2;
  transform: scale(0.98);
}

.contract-btn-flex .btn-icon {
  font-size: 22rpx;
}

.contract-btn-flex .btn-text {
  font-size: 24rpx;
  font-weight: 500;
}



/* 个人荣誉总览 */
.honor-overview-section {
  margin-bottom: 48rpx;
}

.honor-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.stat-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx 16rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #FF7A45;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

/* 勋章墙 */
.medals-section {
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0rpx;
}

.medals-count {
  font-size: 28rpx;
  color: #666666;
}

.share-btn {
  background-color: #4A90E2;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  border: none;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.share-icon {
  font-size: 24rpx;
}

.share-text {
  font-size: 24rpx;
}

.medals-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.medal-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  position: relative;
}

.medal-item.locked {
  opacity: 0.5;
}

.medal-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
  display: block;
}

.medal-name {
  font-size: 24rpx;
  color: #333333;
}

.medal-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.medal-level {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
}

.level-text {
  background-color: #4A90E2;
  color: #FFFFFF;
  font-size: 18rpx;
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
}

.medal-special {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
}

.special-label {
  background-color: #FFD700;
  color: #FFFFFF;
  font-size: 18rpx;
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
}

.medal-progress {
  margin-top: 8rpx;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background-color: #F0F0F0;
  border-radius: 3rpx;
  overflow: hidden;
  margin-bottom: 4rpx;
}

.progress-fill {
  height: 100%;
  background-color: #FF7A45;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 20rpx;
  color: #666666;
}

.medal-unlocked-info {
  margin-top: 8rpx;
}

.unlocked-date {
  font-size: 20rpx;
  color: #4A90E2;
}

.medals-empty {
  text-align: center;
  padding: 60rpx 40rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

/* 家庭荣誉契约 */
.contracts-section {
  margin-bottom: 48rpx;
}

.create-btn {
  background-color: #FF7A45;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  border: none;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.create-icon {
  font-size: 24rpx;
}

.create-text {
  font-size: 24rpx;
}

.contracts-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.contract-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  border-left: 6rpx solid #FF7A45;
}

.contract-header {
  margin-bottom: 0rpx;
}

.contract-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.contract-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.contract-status-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.contract-status-badge.active {
  background-color: #E8F5E8;
  color: #4CAF50;
}

.contract-status-badge.completed {
  background-color: #E3F2FD;
  color: #2196F3;
}

.contract-status-badge.pending-award {
  background-color: #FFF3E0;
  color: #FF9800;
}

.contract-status-badge.cancelled {
  background-color: #FFEBEE;
  color: #F44336;
}

.contract-goal {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.4;
}

.contract-body {
  margin-bottom: 24rpx;
}

.contract-reward {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background-color: #FFF9F5;
  border-radius: 12rpx;
}

.reward-icon {
  font-size: 28rpx;
}

.reward-text {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
}

.contract-progress {
  margin-bottom: 20rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-label {
  font-size: 26rpx;
  color: #666666;
}

.progress-value {
  font-size: 26rpx;
  color: #FF7A45;
  font-weight: bold;
}

.contract-witnesses {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx;
  background-color: #F7F8FA;
  border-radius: 12rpx;
}

.witnesses-label {
  font-size: 24rpx;
  color: #666666;
}

.witnesses-list {
  display: flex;
  gap: 12rpx;
}

.witness-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.witness-avatar {
  font-size: 24rpx;
}

.witness-name {
  font-size: 24rpx;
  color: #333333;
}

.contract-footer {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999999;
}

.contracts-empty {
  text-align: center;
  padding: 80rpx 40rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}


.growth-track-empty {
  text-align: center;
  padding: 60rpx 40rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.camps-empty {
  text-align: center;
  padding: 80rpx 40rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  margin-top: 24rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx 40rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.loading-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
  animation: rotate 2s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
  display: block;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.special-label {
  font-size: 18rpx;
  color: #FF7A45;
  background-color: #FFF2ED;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}

.medal-share-btn {
  position: absolute;
  bottom: 16rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: #4A90E2;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  border: none;
  min-width: 100rpx;
}

.timeline-share-btn {
  position: absolute;
  bottom: 24rpx;
  right: 24rpx;
  background-color: #4A90E2;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  border: none;
  min-width: 100rpx;
}

/* 成长轨迹 */
.growth-track-section {
  margin-bottom: 48rpx;
}

.growth-track-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.track-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}



.track-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.track-info {
  flex: 1;
}

.track-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.track-desc {
  font-size: 24rpx;
  color: #666666;
}

.track-status {
  font-size: 24rpx;
  color: #4A90E2;
  font-weight: 500;
}

.track-date {
  font-size: 24rpx;
  color: #999999;
  display: block;
  margin-top: 4rpx;
}

.track-points {
  font-size: 24rpx;
  color: #FF7A45;
  display: block;
  margin-top: 4rpx;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  display: block;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #b2b2b2;
  display: block;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #b3b1b1;
  line-height: 1.5;
  display: block;
  margin-bottom: 48rpx;
}

.empty-action-btn {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  color: #FFFFFF;
  padding: 20rpx 48rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

/* 分享功能样式 */
.share-all-btn, .share-timeline-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  color: #FFFFFF;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 122, 69, 0.3);
}

.share-all-btn:active, .share-timeline-btn:active {
  transform: translateY(-50%) scale(0.95);
}

.share-icon {
  font-size: 18rpx;
}

.share-text {
  font-size: 20rpx;
}

.medal-content {
  padding: 16rpx;
  width: 100%;
}

.medal-share-btn {
  width: 100%;
  padding: 8rpx 12rpx;
  background-color: #4A90E2;
  color: #FFFFFF;
  border: none;
  border-radius: 0 0 14rpx 14rpx;
  font-size: 20rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.medal-share-btn:active {
  background-color: #357ABD;
  transform: scale(0.98);
}

.timeline-main {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 16rpx 0;
}

.timeline-share-btn {
  padding: 8rpx 16rpx;
  background-color: #4A90E2;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-left: 16rpx;
}

.timeline-share-btn:active {
  background-color: #357ABD;
  transform: scale(0.95);
}



/* 测试按钮样式 */
.test-section {
  padding: 20rpx 32rpx;
  background-color: #FFF3CD;
  border: 1rpx solid #FFEAA7;
  margin: 20rpx 32rpx;
  border-radius: 12rpx;
}

.test-btn {
  background-color: #FF7A45;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  border: none;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 60rpx;
}

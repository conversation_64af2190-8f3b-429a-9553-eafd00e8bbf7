// 孩子管理页面 - 遵循MVS规则

// 引入工具模块
const {
  childrenAPI,
  avatar,
  dataConverter,
  constants,
  userActions,
  childrenActions,
} = require("../../../utils/index.js");
const { APP_CONFIG } = constants;

Page({
  data: {
    // 当前选中的孩子
    currentChild: null,

    // 所有孩子列表
    children: [],

    // 删除确认弹窗
    showDeleteModal: false,
    deleteTarget: {},

    // 加载状态
    loading: false,
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    this.loadCurrentChild();
    this.loadChildrenList();
  },

  /**
   * 页面显示
   */
  onShow() {
    // 每次显示时刷新数据
    this.loadChildrenList();
  },

  /**
   * 加载当前孩子信息
   */
  async loadCurrentChild() {
    try {
      // 调用API获取当前选择的孩子
      const response = await childrenAPI.getCurrentChild();
      const currentChild = response.data || response;

      if (currentChild) {
        // 转换后端数据为前端格式
        const frontendChild =
          dataConverter.convertChildToFrontend(currentChild);
        // 处理头像信息
        const avatarInfo = avatar.getAvatarInfo(frontendChild.avatar, "child");

        this.setData({
          currentChild: {
            ...frontendChild,
            avatar: avatarInfo.url,
            isEmojiAvatar: avatarInfo.isEmoji,
            isDefaultAvatar: avatarInfo.isDefault,
          },
        });
      }
    } catch (error) {
      console.error("加载当前孩子失败:", error);
      // 如果API调用失败，尝试从状态管理器获取
      const currentChild = childrenActions.getCurrentChild();
      if (currentChild) {
        const avatarInfo = avatar.getAvatarInfo(currentChild.avatar, "child");

        this.setData({
          currentChild: {
            ...currentChild,
            avatar: avatarInfo.url,
            isEmojiAvatar: avatarInfo.isEmoji,
            isDefaultAvatar: avatarInfo.isDefault,
          },
        });
      }
    }
  },

  /**
   * 加载孩子列表
   */
  async loadChildrenList() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 调用真实API获取孩子列表
      const response = await childrenAPI.getChildrenList();
      const children = response.data || response;

      // 转换后端数据为前端格式并处理头像
      const processedChildren =
        dataConverter.convertChildrenListToFrontend(children);
      const childrenWithAvatar = avatar.processAvatarList(
        processedChildren,
        "avatar",
        "child"
      );

      this.setData({
        children: childrenWithAvatar,
      });
    } catch (error) {
      console.error("加载孩子列表失败:", error);
      wx.showToast({
        title: "加载失败，请重试",
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 选择孩子
   */
  async selectChild(e) {
    const child = e.currentTarget.dataset.child;

    try {
      // 调用API选择当前孩子
      await childrenAPI.selectCurrentChild(child.id);

      // 更新当前选中的孩子（头像信息已经在列表中处理过了）
      this.setData({
        currentChild: child,
      });

      // 使用状态管理器更新当前孩子
      childrenActions.setCurrentChild(child);

      wx.showToast({
        title: `已切换到${child.name}`,
        icon: "success",
      });
    } catch (error) {
      console.error("选择孩子失败:", error);
      wx.showToast({
        title: "切换失败，请重试",
        icon: "none",
      });
    }
  },

  /**
   * 编辑孩子信息
   */
  editChild(e) {
    const child = e.currentTarget.dataset.child;

    wx.navigateTo({
      url: `/pages/child-create/child-create?mode=edit&childId=${child.id}`,
    });
  },

  /**
   * 删除孩子
   */
  deleteChild(e) {
    const child = e.currentTarget.dataset.child;

    // 检查是否是当前选中的孩子
    if (
      child.id === this.data.currentChild.id &&
      this.data.children.length > 1
    ) {
      wx.showModal({
        title: "提示",
        content: "不能删除当前选中的孩子，请先切换到其他孩子",
        showCancel: false,
      });
      return;
    }

    // 检查是否是唯一的孩子
    if (this.data.children.length === 1) {
      wx.showModal({
        title: "提示",
        content: "至少需要保留一个孩子信息",
        showCancel: false,
      });
      return;
    }

    this.setData({
      deleteTarget: child,
      showDeleteModal: true,
    });
  },

  /**
   * 确认删除
   */
  async confirmDelete() {
    const childId = this.data.deleteTarget.id;

    try {
      // 模拟API调用删除孩子
      await this.deleteChildFromServer(childId);

      // 从列表中移除
      const updatedChildren = this.data.children.filter(
        (child) => child.id !== childId
      );
      this.setData({
        children: updatedChildren,
        showDeleteModal: false,
        deleteTarget: {},
      });

      wx.showToast({
        title: "删除成功",
        icon: "success",
      });
    } catch (error) {
      console.error("删除孩子失败:", error);
      wx.showToast({
        title: "删除失败，请重试",
        icon: "none",
      });
    }
  },

  /**
   * 模拟删除孩子API
   */
  deleteChildFromServer(childId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 1000);
    });
  },

  /**
   * 隐藏删除弹窗
   */
  hideDeleteModal() {
    this.setData({
      showDeleteModal: false,
      deleteTarget: {},
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 跳转到添加孩子页面
   */
  goToAddChild() {
    // 检查孩子数量限制
    if (this.data.children.length >= APP_CONFIG.MAX_CHILDREN) {
      wx.showModal({
        title: "提示",
        content: `最多只能添加${APP_CONFIG.MAX_CHILDREN}个孩子`,
        showCancel: false,
        confirmText: "知道了",
      });
      return;
    }

    wx.navigateTo({
      url: "/pages/child-create/child-create?mode=add",
    });
  },

  /**
   * 导出数据
   */
  exportData() {
    wx.showToast({
      title: "导出功能开发中",
      icon: "none",
    });
  },

  /**
   * 切换账号
   */
  switchAccount() {
    wx.showModal({
      title: "切换账号",
      content: "确定要退出当前账号吗？",
      success: (res) => {
        if (res.confirm) {
          // 清除本地数据
          wx.clearStorageSync();

          // 跳转到登录页
          wx.reLaunch({
            url: "/pages/login/login",
          });
        }
      },
    });
  },
});

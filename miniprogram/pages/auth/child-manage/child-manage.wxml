<!-- 孩子管理页面 -->
<view class="page-container">
    <!-- 顶部添加按钮 -->
    <view class="top-actions">
        <view class="add-child-btn" bindtap="goToAddChild">
            <view class="add-icon">+</view>
            <text>添加孩子</text>
        </view>
    </view>
    <!-- 当前选中的孩子 -->
    <view class="current-child-section" wx:if="{{currentChild.id}}">
        <view class="section-title">
            <text class="emoji">👑</text>
            <text>当前选中的孩子</text>
        </view>
        <view class="current-child-card">
            <view class="child-avatar">
                <view class="default-child-avatar" wx:if="{{currentChild.isEmojiAvatar}}">
                    {{currentChild.avatar}}
                </view>
                <image wx:else src="{{currentChild.avatar}}" mode="aspectFill"></image>
                <view class="current-badge">当前</view>
            </view>
            <view class="child-info">
                <text class="child-name">{{currentChild.name}}</text>
                <text class="child-age">
                    {{currentChild.age}}岁 · {{currentChild.gender === 'male' ? '男孩' : '女孩'}}
                </text>
                <text class="child-birthday">生日：{{currentChild.birthday}}</text>
            </view>
            <view class="child-stats">
                <view class="stat-item">
                    <text class="stat-number">{{currentChild.totalCheckins || 0}}</text>
                    <text class="stat-label">总打卡</text>
                </view>
                <view class="stat-item">
                    <text class="stat-number">{{currentChild.totalPoints || 0}}</text>
                    <text class="stat-label">积分</text>
                </view>
                <view class="stat-item">
                    <text class="stat-number">{{currentChild.currentStreak || 0}}</text>
                    <text class="stat-label">连续天数</text>
                </view>
            </view>
        </view>
    </view>
    <!-- 所有孩子列表 -->
    <view class="children-list-section">
        <view class="section-title">
            <text class="emoji">👨‍👩‍👧‍👦</text>
            <text>所有孩子 ({{children.length}})</text>
        </view>
        <!-- 孩子列表 -->
        <view class="children-list" wx:if="{{children.length > 0}}">
            <view class="child-item {{item.id === currentChild.id ? 'current' : ''}}" wx:for="{{children}}" wx:key="id" bindtap="selectChild" data-child="{{item}}">
                <view class="child-left">
                    <view class="child-avatar-small">
                        <view class="default-child-avatar-small" wx:if="{{item.isEmojiAvatar}}">
                            {{item.avatar}}
                        </view>
                        <image wx:else src="{{item.avatar}}" mode="aspectFill"></image>
                        <view class="current-indicator" wx:if="{{item.id === currentChild.id}}">
                            ✓
                        </view>
                    </view>
                    <view class="child-info-small">
                        <text class="child-name-small">{{item.name}}</text>
                        <text class="child-details">
                            {{item.age}}岁 · {{item.gender === 'male' ? '男孩' : '女孩'}}
                        </text>
                        <text class="child-progress">已打卡{{item.totalCheckins || 0}}次</text>
                    </view>
                </view>
                <view class="child-right">
                    <view class="child-actions">
                        <view class="action-btn edit" bindtap="editChild" data-child="{{item}}" catchtap="stopPropagation">
                            <view class="edit-small-icon">✏️</view>
                        </view>
                        <view class="action-btn delete" bindtap="deleteChild" data-child="{{item}}" catchtap="stopPropagation">
                            <view class="delete-icon">🗑️</view>
                        </view>
                    </view>
                    <view class="child-score">
                        <text class="score-number">{{item.totalPoints || 0}}</text>
                        <text class="score-label">积分</text>
                    </view>
                </view>
            </view>
        </view>
        <!-- 空状态 -->
        <view class="empty-state" wx:else>
            <view class="empty-illustration">
                <view class="empty-state-emoji">📭</view>
            </view>
            <view class="empty-content">
                <text class="empty-title">👶 还没有添加孩子</text>
                <text class="empty-subtitle">添加孩子信息，开始跳绳学习之旅吧！</text>
                <button class="btn btn-primary" bindtap="goToAddChild">添加第一个孩子</button>
            </view>
        </view>
    </view>
    <!-- 管理功能区域 -->
    <view class="management-section" wx:if="{{children.length > 0}}">
        <view class="section-title">
            <text class="emoji">⚙️</text>
            <text>管理功能</text>
        </view>
        <view class="management-actions">
            <view class="management-item" bindtap="goToAddChild">
                <view class="management-icon">
                    <view class="add-child-icon">👶➕</view>
                </view>
                <view class="management-content">
                    <text class="management-title">添加新孩子</text>
                    <text class="management-subtitle">为家庭添加更多孩子</text>
                </view>
                <view class="management-arrow">></view>
            </view>
            <view class="management-item" bindtap="exportData">
                <view class="management-icon">
                    <view class="export-icon">📤</view>
                </view>
                <view class="management-content">
                    <text class="management-title">导出数据</text>
                    <text class="management-subtitle">导出孩子的学习记录</text>
                </view>
                <view class="management-arrow">></view>
            </view>
            <view class="management-item" bindtap="switchAccount">
                <view class="management-icon">
                    <view class="switch-icon">🔄</view>
                </view>
                <view class="management-content">
                    <text class="management-title">切换账号</text>
                    <text class="management-subtitle">使用其他微信账号登录</text>
                </view>
                <view class="management-arrow">></view>
            </view>
        </view>
    </view>
    <!-- 底部安全距离 -->
    <view class="safe-area-bottom"></view>
</view>
<!-- 删除确认弹窗 -->
<view class="delete-modal {{showDeleteModal ? 'show' : ''}}" bindtap="hideDeleteModal">
    <view class="modal-content" catchtap="stopPropagation">
        <view class="modal-header">
            <text class="modal-title">确认删除</text>
        </view>
        <view class="modal-body">
            <view class="delete-warning">
                <text class="warning-icon">⚠️</text>
                <text class="warning-text">确定要删除 "{{deleteTarget.name}}" 的信息吗？</text>
            </view>
            <view class="delete-consequences">
                <text class="consequence-title">删除后将会：</text>
                <view class="consequence-list">
                    <text class="consequence-item">• 清除所有打卡记录</text>
                    <text class="consequence-item">• 清除所有积分和成就</text>
                    <text class="consequence-item">• 无法恢复相关数据</text>
                </view>
            </view>
        </view>
        <view class="modal-footer">
            <button class="btn btn-outline" bindtap="hideDeleteModal">取消</button>
            <button class="btn btn-danger" bindtap="confirmDelete">确认删除</button>
        </view>
    </view>
</view>
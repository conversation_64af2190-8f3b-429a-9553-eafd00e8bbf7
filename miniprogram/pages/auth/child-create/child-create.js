// 添加/编辑孩子页面 - 遵循MVS规则
const {
  childrenAPI,
  avatar,
  dataConverter,
} = require("../../../utils/index.js");
const { childrenActions } = require("../../../utils/state-actions.js");

Page({
  data: {
    // 页面模式：add 添加，edit 编辑
    mode: "add",

    // 编辑时的孩子ID
    child_id: null,

    // 表单数据（简化版）
    formData: {
      name: "",
      gender: "",
      birthday: "",
      nickname: "",
    },

    // 计算出的年龄
    calculatedAge: 0,

    // 当前日期
    currentDate: "",

    // 孩子默认头像
    childAvatar: "",

    // 是否可以保存
    canSave: false,

    // 保存状态
    saving: false,
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    const mode = options.mode || "add";
    const childId = options.child_id;

    // 设置默认头像
    const defaultAvatar = avatar.getChildAvatar("");

    this.setData({
      mode: mode,
      child_id: childId,
      currentDate: this.getCurrentDate(),
      childAvatar: defaultAvatar,
    });

    // 如果是编辑模式，加载孩子信息
    if (mode === "edit" && childId) {
      this.loadChildInfo(childId);
    }

    // 检查表单有效性
    this.checkFormValidity();
  },

  /**
   * 获取当前日期
   */
  getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  },

  /**
   * 加载孩子信息（编辑模式）
   */
  async loadChildInfo(childId) {
    try {
      // 调用真实API获取孩子信息
      const response = await childrenAPI.getChildDetail(childId);
      const childInfo = response.data || response;

      // 转换后端数据为前端格式
      const frontendData = dataConverter.convertChildToFrontend(childInfo);

      // 更新表单数据
      this.setData({
        formData: {
          name: frontendData.name || "",
          gender: frontendData.gender || "",
          birthday: frontendData.birthday || "",
          nickname: frontendData.nickname || "",
        },
        childAvatar: avatar.getChildAvatar(frontendData.avatar),
      });

      // 计算年龄
      if (frontendData.birthday) {
        this.calculateAge(frontendData.birthday);
      }

      // 检查表单有效性
      this.checkFormValidity();
    } catch (error) {
      console.error("加载孩子信息失败:", error);
      wx.showToast({
        title: "加载失败，请重试",
        icon: "none",
      });
    }
  },

  /**
   * 姓名输入
   */
  onNameInput(e) {
    this.setData({
      "formData.name": e.detail.value,
    });
    this.checkFormValidity();
  },

  /**
   * 昵称输入
   */
  onNicknameInput(e) {
    this.setData({
      "formData.nickname": e.detail.value,
    });
  },

  /**
   * 选择性别
   */
  selectGender(e) {
    const gender = e.currentTarget.dataset.gender;
    this.setData({
      "formData.gender": gender,
    });
    this.checkFormValidity();
  },

  /**
   * 生日变化
   */
  onBirthdayChange(e) {
    const birthday = e.detail.value;
    this.setData({
      "formData.birthday": birthday,
    });

    this.calculateAge(birthday);
    this.checkFormValidity();
  },

  /**
   * 计算年龄
   */
  calculateAge(birthday) {
    const age = dataConverter.calculateAge(birthday);
    this.setData({
      calculatedAge: age,
    });
  },

  /**
   * 检查表单有效性
   */
  checkFormValidity() {
    const { name, gender } = this.data.formData;
    // 只要求姓名和性别为必填项
    const canSave = name.trim() && gender;

    this.setData({
      canSave: canSave,
    });
  },

  /**
   * 保存孩子信息
   */
  async saveChild() {
    if (!this.data.canSave || this.data.saving) return;

    this.setData({ saving: true });

    try {
      if (this.data.mode === "edit") {
        // 编辑模式：调用更新API
        const updateData = dataConverter.convertChildToBackendUpdate(
          this.data.formData
        );
        const updatedChild = await childrenAPI.updateChild(
          this.data.child_id,
          updateData
        );

        // 同步更新后的数据
        await childrenActions.syncAfterChildUpdated(
          this.data.child_id,
          updatedChild
        );

        wx.showToast({
          title: "修改成功",
          icon: "success",
        });
      } else {
        // 添加模式：调用创建API
        const createData = dataConverter.convertChildToBackendCreate(
          this.data.formData
        );
        const newChild = await childrenAPI.createChild(createData);

        // 同步创建后的数据
        await childrenActions.syncAfterChildCreated(newChild);

        wx.showToast({
          title: "添加成功",
          icon: "success",
        });
      }

      // 延迟返回，让用户看到成功提示
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      console.error("保存失败:", error);
      wx.showToast({
        title: "保存失败，请重试",
        icon: "none",
      });
    } finally {
      this.setData({ saving: false });
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },
});
